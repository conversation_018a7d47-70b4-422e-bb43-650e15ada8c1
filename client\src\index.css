@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import "tailwindcss";

@import "tailwindcss";

@theme {
    --color-primary-blue: hsl(180, 66%, 49%);
    --color-primary-purple: hsl(257, 27%, 26%);
    --color-secondary-red: hsl(0, 87%, 67%);
    --color-neutral-gray400: hsl(0, 0%, 75%);
    --color-neutral-gray500: hsl(257, 7%, 63%);
    --color-neutral-gray900: hsl(255, 11%, 22%);
    --color-neutral-gray950: hsl(260, 8%, 14%);
}


.background {
    background: url(./assets/svg/bg-boost-desktop.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #3a3053;
}

.backgroundSearch {
    background: url(./assets/svg/bg-shorten-desktop.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #717075 transparent;
}

::-webkit-scrollbar {
    display: none;
}